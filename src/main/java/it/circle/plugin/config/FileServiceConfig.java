package it.circle.plugin.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.minio.BucketExistsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import it.circle.crm.api.file.CrmFileService;
import it.circle.crm.file.FileService;
import it.circle.crm.file.MinioFileService;
import it.circle.crm.file.DefaultCrmFileService;
import it.circle.crm.file.MinioFileService.MinioClientInfo;

@Configuration
public class FileServiceConfig {

	@Value("${minio.url:#{null}}")
	private String minioUrl;

	@Value("${minio.accessKey:#{null}}")
	private String accessKey;

	@Value("${minio.secretKey:#{null}}")
	private String secretKey;

	@Value("${minio.bucketName:#{null}}")
	private String bucketName;

	@Bean
	public MinioClientInfo minioClientInfo() {
		if (minioUrl == null || minioUrl.isEmpty() || bucketName == null || bucketName.isEmpty()) {
			return null;
		}

		MinioClient client = MinioClient.builder()
				.endpoint(minioUrl)
				.credentials(accessKey, secretKey)
				.build();

		try {
			boolean exists = client.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
			if (!exists) {
				client.makeBucket(MakeBucketArgs.builder()
						.bucket(bucketName)
						.build());
			}
		} catch (Exception ex) {
		}

		MinioClientInfo clientInfo = new MinioClientInfo();
		clientInfo.setClient(client);
		clientInfo.setBucketName(bucketName);

		return clientInfo;
	}

	@Bean
	public FileService fileService()
	{
		MinioClientInfo info = minioClientInfo();
		if (info == null) {
			return null;
		}

		FileService service = new MinioFileService();
		service.init();
		return service;
	}

	@Bean
	public CrmFileService crmFileService()
	{
		FileService service = fileService();
		if (service == null) {
			return null;
		}
		return new DefaultCrmFileService(service);
	}
}
