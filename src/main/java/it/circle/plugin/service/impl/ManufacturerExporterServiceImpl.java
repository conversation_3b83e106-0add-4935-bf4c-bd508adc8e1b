package it.circle.plugin.service.impl;

import it.circle.crm.data.manufacturer.document.ManufacturerDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.ManufacturerDocumentRepository;
import it.circle.plugin.service.*;
import it.circle.plugin.utility.ExportUtility;
import it.circle.plugin.utility.MinioFileUploadUtility;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ReflectionUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class ManufacturerExporterServiceImpl implements ManufacturerExporterService {

    private final ManufacturerDocumentRepository manufacturerRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;
    private final ReflectionUtility reflectionUtility;
    private final ExporterEntityService exporterEntityService;
    private final MinioFileUploadUtility minioFileUploadUtility;

    @Override
    public void processManufacturerExport(String instanceId, String owner, Target target) {
        log.info("Processing Manufacturer export for instance: {}, owner: {} (legacy method)", instanceId, owner);
        processManufacturerExport(instanceId, owner, target, null);
    }

    @Override
    public void processManufacturerExport(String instanceId, String owner, Target target, String exportId) {
        log.info("Processing Manufacturer export for instance: {}, owner: {}, exportId: {}", instanceId, owner, exportId);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all manufacturers for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch manufacturer data from database
            List<ManufacturerDocument> manufacturers = mongoTemplate.find(query, ManufacturerDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> manufacturerMeta = fieldMetadataService.buildFieldMetadataMap("Manufacturer", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (manufacturerMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping", field);
                }
            }

            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            for (FieldsLinee fieldsLinee : lineConfigs) {
                Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                List<String> validLineFields = new ArrayList<>();
                for (String field : fieldsLinee.getFields()) {
                    if (subMeta.containsKey(field)) {
                        validLineFields.add(field);
                    } else {
                        log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping", fieldsLinee.getEntity(), field);
                    }
                }

                if (!validLineFields.isEmpty()) {
                    validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                } else {
                    log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} manufacturers matching criteria", manufacturers.size());

            if (manufacturers.isEmpty()) {
                log.warn("No manufacturers found for the given criteria!");
                ExportUtility.logExportSummary("Manufacturer", instanceId, owner, 0, "No manufacturers found");
                return;
            }

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Manufacturer", instanceId);
            log.info("Built field metadata map with {} fields for Manufacturer entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (ManufacturerDocument manufacturer : manufacturers) {
                // Header row
                Map<String, Object> headerMap = reflectionUtility.extractFields(manufacturer, validHeaderFields);

                // If there are no line configurations, just add the header data
                if (validLineConfigs.isEmpty()) {
                    exportData.add(headerMap);
                } else {
                    // For each line config (if any sub-entities exist for Manufacturer)
                    for (FieldsLinee fieldsLinee : validLineConfigs) {
                        String subFieldName = fieldsLinee.getEntity();
                        List<?> subRows = (List<?>) reflectionUtility.getFieldValue(manufacturer, subFieldName);
                        if (subRows != null) {
                            for (Object row : subRows) {
                                Map<String, Object> lineMap = reflectionUtility.extractFields(row, fieldsLinee.getFields());
                                Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                                combined.putAll(lineMap);
                                exportData.add(combined);
                            }
                        }
                    }
                }
            }

            String fileName = ExportUtility.generateFileName("Manufacturer", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Manufacturers", fileName
            );

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            // Upload to MinIO if available and exportId is provided
            if (exportId != null && minioFileUploadUtility.isMinioAvailable()) {
                try {
                    String downloadUri = minioFileUploadUtility.uploadExcelFile(excelFile, fileName, instanceId, instanceId);
                    log.info("Excel file uploaded to MinIO: {}", downloadUri);
                    exporterEntityService.completeExport(exportId, downloadUri);
                    log.info("Export {} completed successfully with download link", exportId);
                } catch (Exception e) {
                    log.error("Failed to upload file to MinIO for export {}: {}", exportId, e.getMessage(), e);
                    exporterEntityService.failExport(exportId, "Failed to upload file to MinIO: " + e.getMessage());
                    throw e;
                }
            } else {
                // Fallback to local file system for backward compatibility
                String projectDir = System.getProperty("user.dir");
                String exportPath = projectDir + "/exports";
                String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
                log.info("Excel file saved in project directory: {}", savedPath);
                if (exportId != null) {
                    exporterEntityService.completeExport(exportId, "Local file: " + savedPath);
                }
            }

            log.info("Successfully completed Manufacturer export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Manufacturer export for instance: {}, exportId: {}", instanceId, exportId, e);
            if (exportId != null) {
                try {
                    exporterEntityService.failExport(exportId, "Manufacturer export failed: " + e.getMessage());
                } catch (Exception failException) {
                    log.error("Failed to update export status for exportId: {}", exportId, failException);
                }
            }
            throw new RuntimeException("Failed to process Manufacturer export", e);
        }
    }


}
