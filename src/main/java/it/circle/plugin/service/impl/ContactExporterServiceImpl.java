package it.circle.plugin.service.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import it.circle.crm.data.contact.document.ContactDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.ContactDocumentRepository;
import it.circle.plugin.service.ContactExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.service.ExporterEntityService;
import it.circle.plugin.service.FieldValidationService;
import it.circle.plugin.service.FieldMetadataService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import it.circle.plugin.utility.MinioFileUploadUtility;
import it.circle.plugin.utility.ReflectionUtility;
import it.circle.plugin.event.FileCompletionEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.Decimal128;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;
import java.util.Date;
import java.util.Arrays;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContactExporterServiceImpl implements ContactExporterService {

    private final ContactDocumentRepository contactRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;
    private final ReflectionUtility reflectionUtility;
    private final ExporterEntityService exporterEntityService;
    private final MinioFileUploadUtility minioFileUploadUtility;
    private final FileCompletionEvent fileCompletionEvent;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    @Override
    public void processContactExport(String instanceId, String owner, Target target) {
        log.info("Processing Contact export for instance: {}, owner: {} (legacy method)", instanceId, owner);
        // Delegate to the new method with null exportId for backward compatibility
        processContactExport(instanceId, owner, target, null);
    }

    @Override
    public void processContactExport(String instanceId, String owner, Target target, String exportId) {
        log.info("Processing Contact export for instance: {}, owner: {}, exportId: {}", instanceId, owner, exportId);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all contacts for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch contact data from database
            List<ContactDocument> contacts = mongoTemplate.find(query, ContactDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> contactMeta = fieldMetadataService.buildFieldMetadataMap("Contact", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (contactMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping", field);
                }
            }

            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            for (FieldsLinee fieldsLinee : lineConfigs) {
                Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                List<String> validLineFields = new ArrayList<>();
                for (String field : fieldsLinee.getFields()) {
                    if (subMeta.containsKey(field)) {
                        validLineFields.add(field);
                    } else {
                        log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping", fieldsLinee.getEntity(), field);
                    }
                }

                if (!validLineFields.isEmpty()) {
                    validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                } else {
                    log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} contacts matching criteria", contacts.size());

            if (contacts.isEmpty()) {
                log.warn("No contacts found for the given criteria!");
                ExportUtility.logExportSummary("Contact", instanceId, owner, 0, "No contacts found");
                return;
            }

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Contact", instanceId);
            log.info("Built field metadata map with {} fields for Contact entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (ContactDocument contact : contacts) {
                // Header row
                Map<String, Object> headerMap = reflectionUtility.extractFields(contact, validHeaderFields);

                // If there are no line configurations, just add the header data
                if (validLineConfigs.isEmpty()) {
                    exportData.add(headerMap);
                } else {
                    // For each line config (if any sub-entities exist for Contact)
                    for (FieldsLinee fieldsLinee : validLineConfigs) {
                        String subFieldName = fieldsLinee.getEntity();
                        List<?> subRows = (List<?>) reflectionUtility.getFieldValue(contact, subFieldName);
                        if (subRows != null) {
                            for (Object row : subRows) {
                                Map<String, Object> lineMap = reflectionUtility.extractFields(row, fieldsLinee.getFields());
                                Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                                combined.putAll(lineMap);
                                exportData.add(combined);
                            }
                        }
                    }
                }
            }

            String fileName = ExportUtility.generateFileName("Contact", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Contacts", fileName
            );

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            // Upload to MinIO if available and exportId is provided
            if (exportId != null && minioFileUploadUtility.isMinioAvailable()) {
                try {
                    String downloadUri = minioFileUploadUtility.uploadExcelFile(excelFile, fileName, instanceId, instanceId);
                    log.info("Excel file uploaded to MinIO: {}", downloadUri);

                    // Update export status with download link
                    exporterEntityService.completeExport(exportId, downloadUri);
                    log.info("Export {} completed successfully with download link", exportId);

                    // Send TaskOp notification - Excel exported successfully
                    fileCompletionEvent.sendExportCompletedNotification(exportId, owner, instanceId, instanceId,
                        Arrays.asList(fileName));

                } catch (Exception e) {
                    log.error("Failed to upload file to MinIO for export {}: {}", exportId, e.getMessage(), e);
                    exporterEntityService.failExport(exportId, "Failed to upload file to MinIO: " + e.getMessage());
                    throw e;
                }
            } else {
                // Fallback to local file system for backward compatibility
                String projectDir = System.getProperty("user.dir");
                String exportPath = projectDir + "/exports";
                String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
                log.info("Excel file saved in project directory: {}", savedPath);

                if (exportId != null) {
                    // Complete export without download link for local storage
                    exporterEntityService.completeExport(exportId, "Local file: " + savedPath);

                    // Send TaskOp notification - Excel exported successfully (local storage)
                    fileCompletionEvent.sendExportCompletedNotification(exportId, owner, instanceId, instanceId,
                        Arrays.asList(fileName));
                }
            }

            log.info("Successfully completed Contact export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Contact export for instance: {}, exportId: {}", instanceId, exportId, e);

            // Mark export as failed if exportId is provided
            if (exportId != null) {
                try {
                    exporterEntityService.failExport(exportId, "Contact export failed: " + e.getMessage());
                } catch (Exception failException) {
                    log.error("Failed to update export status for exportId: {}", exportId, failException);
                }
            }

            throw new RuntimeException("Failed to process Contact export", e);
        }
    }
}
