package it.circle.plugin.event;

import it.circle.crm.api.task.TaskStatus;
import it.circle.crm.api.task.model.TaskOp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FileCompletionEvent {

    @Autowired
    private Environment env;

    /**
     * Create and send a task notification for export completion
     *
     * @param exportId The export entity ID
     * @param description Description of the export task
     * @param status Task status (COMPLETED, FAILED, etc.)
     * @param percentage Progress percentage (0-100)
     * @param owner Owner of the export
     * @param instanceId Instance ID
     * @param companyId Company ID
     * @param fileNames List of file names being exported
     */
    public void sendExportCompletionNotification(String exportId, String description, TaskStatus status,
                                                int percentage, String owner, String instanceId,
                                                String companyId, List<String> fileNames) {
        try {
            log.info("Creating export completion notification for export: {}, status: {}", exportId, status);

            // Create notification
            TaskOp op = new TaskOp();
            op.setId(exportId)
              .setDescription(description != null ? description :
                  String.format("DATA EXPORT - Files: %s",
                      fileNames != null && !fileNames.isEmpty() ?
                          String.join(", ", fileNames) : "Unknown"))
              .setType("EXPORTER_ENTITY")
              .setStatus(status)
              .setPercentage(Math.max(0, Math.min(100, percentage)))
              .setOwner(owner)
              .setInstanceId(instanceId)
              .setCompanyId(companyId);

            // Send to queue
            sendStatus(op);

            log.info("Export completion notification sent successfully for export: {}", exportId);

        } catch (Exception e) {
            log.error("Failed to send export completion notification for export {}: {}", exportId, e.getMessage(), e);
            // Don't re-throw as this is a notification and shouldn't break the main flow
        }
    }

    /**
     * Send task notification for export started
     */
    public void sendExportStartedNotification(String exportId, String owner, String instanceId,
                                            String companyId, List<String> fileNames) {
        sendExportCompletionNotification(exportId, null, TaskStatus.STARTED, 0,
                                       owner, instanceId, companyId, fileNames);
    }

    /**
     * Send task notification for export in progress
     */
    public void sendExportProgressNotification(String exportId, int percentage, String owner,
                                             String instanceId, String companyId, List<String> fileNames) {
        sendExportCompletionNotification(exportId, null, TaskStatus.PROCESSING, percentage,
                                       owner, instanceId, companyId, fileNames);
    }

    /**
     * Send task notification for export completed
     */
    public void sendExportCompletedNotification(String exportId, String owner, String instanceId,
                                              String companyId, List<String> fileNames) {
        sendExportCompletionNotification(exportId, null, TaskStatus.PROCESSED, 100,
                                       owner, instanceId, companyId, fileNames);
    }

    /**
     * Send task notification for export failed
     */
    public void sendExportFailedNotification(String exportId, String errorMessage, String owner,
                                           String instanceId, String companyId, List<String> fileNames) {
        String description = String.format("DATA EXPORT FAILED - Files: %s - Error: %s",
            fileNames != null && !fileNames.isEmpty() ? String.join(", ", fileNames) : "Unknown",
            errorMessage != null ? errorMessage : "Unknown error");

        sendExportCompletionNotification(exportId, description, TaskStatus.FAILED, 0,
                                       owner, instanceId, companyId, fileNames);
    }

    /**
     * Send TaskOp to the crm.tasks queue
     */
    private void sendStatus(TaskOp task) throws Exception {
        log.debug("Sending task status to queue: {}", task);

        it.circle.plugin.lib.mqrabbit.MqRabbitConnectionEntity connectionEntity = new it.circle.plugin.lib.mqrabbit.MqRabbitConnectionEntity();
        connectionEntity.setHost(env.getProperty("spring.rabbitmq.host"));
        connectionEntity.setUser(env.getProperty("spring.rabbitmq.username"));
        connectionEntity.setPassword(env.getProperty("spring.rabbitmq.password"));
        connectionEntity.setVirtualHost(env.getProperty("spring.rabbitmq.virtual-host"));
        connectionEntity.setQueueName(env.getProperty("queue.producer.crmtasks"));

        it.circle.plugin.lib.mqrabbit.MqRabbitProducer.producerToQueue(connectionEntity, task);

        log.debug("Task status sent successfully to queue: {}", connectionEntity.getQueueName());
    }
}
