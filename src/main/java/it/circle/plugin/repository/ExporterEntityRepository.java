package it.circle.plugin.repository;

import it.circle.crm.data.exporter.ExporterEntityDocument;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface ExporterEntityRepository extends MongoRepository<ExporterEntityDocument, String> {

    /**
     * Find export entity by instance ID and status
     */
    @Query("{'instanceId': ?0, 'status': ?1}")
    List<ExporterEntityDocument> findByInstanceIdAndStatus(String instanceId, ExporterEntityDocument.Status status);

    /**
     * Find export entity by instance ID
     */
    @Query("{'instanceId': ?0}")
    List<ExporterEntityDocument> findByInstanceId(String instanceId);

    /**
     * Find export entities by status
     */
    @Query("{'status': ?0}")
    List<ExporterEntityDocument> findByStatus(ExporterEntityDocument.Status status);

    /**
     * Find export entities created after a specific date
     */
    @Query("{'startDate': {$gte: ?0}}")
    List<ExporterEntityDocument> findByStartDateAfter(Instant startDate);

    /**
     * Find export entities by instance ID and date range
     */
    @Query("{'instanceId': ?0, 'startDate': {$gte: ?1, $lte: ?2}}")
    List<ExporterEntityDocument> findByInstanceIdAndDateRange(String instanceId, Instant startDate, Instant endDate);

    /**
     * Find pending or in-progress exports for an instance
     */
    @Query("{'instanceId': ?0, 'status': {$in: ['PENDING', 'IN_PROGRESS']}}")
    List<ExporterEntityDocument> findActiveExportsByInstanceId(String instanceId);
}
