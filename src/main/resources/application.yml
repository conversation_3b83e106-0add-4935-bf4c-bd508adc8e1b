company-name: ${COMPANY:virtual}
plugin-id: crm.pluginexporter

server:
  port: ${SERVER_PORT:8092}

spring:
  datasource:
    url: ${DB_URL:*****************************************}
    username: ${DB_USERNAME:plugin_dev}
    password: ${DB_PASSWORD:Plugin_dev_123456789}
    driver-class-name: ${DB_DRIVER:com.mysql.cj.jdbc.Driver}
  main:
    allow-bean-definition-overriding: true
  batch:
    jdbc:
      initialize-schema: always
    job:
      enabled: false
  jpa:
    show-sql: false
    generate-ddl: true
    hibernate:
      ddl-auto: update
  rabbitmq:
    host: ${RABBITMQ_HOST:************}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USERNAME:plugin}
    password: ${RABBITMQ_PASSWORD:zNcz9T7dv9m5MPNz8xep}
    virtual-host: ${RABBITMQ_VHOST:dev}
  data:
    mongodb:
      uri: ${MONGODB_URI:****************************************************************}

schedule:
  defaultsecondinterval: ${DEFAULT_SECOND_INTERVAL:3600}

queue:
  name:
    exporter: ${plugin-id}-exporter
  create:
    exporter: true

minio:
  url: https://minio.circlecrm.cloud       # Replace with your MinIO endpoint
  accessKey: SdVozY5AxSF3TJpKM53O # Replace with actual access key
  secretKey: mHAaP1YVYIwLSYIdNJhI6NVSWk3OEbOaXUv9lxt8 # Replace with actual secret key
  bucketName: crm-dev            # Replace with your bucket name

