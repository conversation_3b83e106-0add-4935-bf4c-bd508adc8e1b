package it.circle.plugin.utility;

import it.circle.crm.api.file.CrmFile;
import it.circle.crm.api.file.CrmFileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MinioFileUploadUtilityTest {

    @Mock
    private CrmFileService crmFileService;

    private MinioFileUploadUtility minioFileUploadUtility;

    @BeforeEach
    void setUp() {
        minioFileUploadUtility = new MinioFileUploadUtility(crmFileService);
    }

    @Test
    void testIsMinioAvailable_WhenServiceExists() {
        assertTrue(minioFileUploadUtility.isMinioAvailable());
    }

    @Test
    void testIsMinioAvailable_WhenServiceIsNull() {
        MinioFileUploadUtility utilityWithNullService = new MinioFileUploadUtility(null);
        assertFalse(utilityWithNullService.isMinioAvailable());
    }

    @Test
    void testUploadExcelFile_Success() {
        // Arrange
        ByteArrayOutputStream excelFile = new ByteArrayOutputStream();
        excelFile.writeBytes("test excel content".getBytes());
        String fileName = "test-export";
        String companyId = "company123";
        String instanceId = "instance456";
        String expectedUri = "https://minio.example.com/bucket/test-export.xlsx";

        CrmFile mockCrmFile = new CrmFile();
        mockCrmFile.setUri(expectedUri);
        mockCrmFile.setFilename(fileName + ".xlsx");
        mockCrmFile.setSize(excelFile.size());

        when(crmFileService.uploadFile(anyString(), anyString(), any(MultipartFile.class)))
            .thenReturn(mockCrmFile);

        // Act
        String result = minioFileUploadUtility.uploadExcelFile(excelFile, fileName, companyId, instanceId);

        // Assert
        assertEquals(expectedUri, result);
        verify(crmFileService).uploadFile(eq(companyId), eq(instanceId), any(MultipartFile.class));
    }

    @Test
    void testUploadExcelFile_ServiceThrowsException() {
        // Arrange
        ByteArrayOutputStream excelFile = new ByteArrayOutputStream();
        excelFile.writeBytes("test excel content".getBytes());
        String fileName = "test-export";
        String companyId = "company123";
        String instanceId = "instance456";

        when(crmFileService.uploadFile(anyString(), anyString(), any(MultipartFile.class)))
            .thenThrow(new RuntimeException("MinIO connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            minioFileUploadUtility.uploadExcelFile(excelFile, fileName, companyId, instanceId);
        });

        assertTrue(exception.getMessage().contains("Failed to upload file to MinIO"));
        assertTrue(exception.getMessage().contains("MinIO connection failed"));
    }

    @Test
    void testUploadAndGetMetadata_Success() {
        // Arrange
        ByteArrayOutputStream excelFile = new ByteArrayOutputStream();
        excelFile.writeBytes("test excel content".getBytes());
        String fileName = "test-export";
        String companyId = "company123";
        String instanceId = "instance456";

        CrmFile mockCrmFile = new CrmFile();
        mockCrmFile.setUri("https://minio.example.com/bucket/test-export.xlsx");
        mockCrmFile.setFilename(fileName + ".xlsx");
        mockCrmFile.setSize(excelFile.size());

        when(crmFileService.uploadFile(anyString(), anyString(), any(MultipartFile.class)))
            .thenReturn(mockCrmFile);

        // Act
        CrmFile result = minioFileUploadUtility.uploadAndGetMetadata(excelFile, fileName, companyId, instanceId);

        // Assert
        assertNotNull(result);
        assertEquals(mockCrmFile.getUri(), result.getUri());
        assertEquals(mockCrmFile.getFilename(), result.getFilename());
        assertEquals(mockCrmFile.getSize(), result.getSize());
        verify(crmFileService).uploadFile(eq(companyId), eq(instanceId), any(MultipartFile.class));
    }

    @Test
    void testUploadExcelFile_EmptyFile() {
        // Arrange
        ByteArrayOutputStream emptyFile = new ByteArrayOutputStream();
        String fileName = "empty-export";
        String companyId = "company123";
        String instanceId = "instance456";

        CrmFile mockCrmFile = new CrmFile();
        mockCrmFile.setUri("https://minio.example.com/bucket/empty-export.xlsx");
        mockCrmFile.setFilename(fileName + ".xlsx");
        mockCrmFile.setSize(0);

        when(crmFileService.uploadFile(anyString(), anyString(), any(MultipartFile.class)))
            .thenReturn(mockCrmFile);

        // Act
        String result = minioFileUploadUtility.uploadExcelFile(emptyFile, fileName, companyId, instanceId);

        // Assert
        assertNotNull(result);
        assertEquals(mockCrmFile.getUri(), result);
    }

    @Test
    void testUploadExcelFile_NullParameters() {
        // Arrange
        ByteArrayOutputStream excelFile = new ByteArrayOutputStream();
        excelFile.writeBytes("test content".getBytes());

        // Act & Assert - Test null fileName
        assertThrows(RuntimeException.class, () -> {
            minioFileUploadUtility.uploadExcelFile(excelFile, null, "company", "instance");
        });

        // Act & Assert - Test null companyId
        assertThrows(RuntimeException.class, () -> {
            minioFileUploadUtility.uploadExcelFile(excelFile, "filename", null, "instance");
        });

        // Act & Assert - Test null instanceId
        assertThrows(RuntimeException.class, () -> {
            minioFileUploadUtility.uploadExcelFile(excelFile, "filename", "company", null);
        });
    }
}
