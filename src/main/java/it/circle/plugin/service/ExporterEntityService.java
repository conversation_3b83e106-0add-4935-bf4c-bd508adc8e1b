package it.circle.plugin.service;

import it.circle.crm.data.exporter.ExporterEntityDocument;
import it.circle.plugin.model.Target;

import java.util.List;
import java.util.Optional;

public interface ExporterEntityService {

    /**
     * Create a new export entity document
     * 
     * @param instanceId The instance ID
     * @param companyId The company ID
     * @param targets List of export targets
     * @return Created ExporterEntityDocument
     */
    ExporterEntityDocument createExport(String instanceId, String companyId, List<Target> targets);

    /**
     * Update export status
     * 
     * @param exportId The export document ID
     * @param status New status
     * @return Updated document
     */
    ExporterEntityDocument updateStatus(String exportId, ExporterEntityDocument.Status status);

    /**
     * Update export progress percentage
     * 
     * @param exportId The export document ID
     * @param percentage Progress percentage (0-100)
     * @return Updated document
     */
    ExporterEntityDocument updateProgress(String exportId, Integer percentage);

    /**
     * Mark export as completed with download link
     * 
     * @param exportId The export document ID
     * @param downloadLink The MinIO download link
     * @return Updated document
     */
    ExporterEntityDocument completeExport(String exportId, String downloadLink);

    /**
     * Mark export as failed with error message
     * 
     * @param exportId The export document ID
     * @param errorMessage Error description
     * @return Updated document
     */
    ExporterEntityDocument failExport(String exportId, String errorMessage);

    /**
     * Find export by ID
     * 
     * @param exportId The export document ID
     * @return Optional ExporterEntityDocument
     */
    Optional<ExporterEntityDocument> findById(String exportId);

    /**
     * Find exports by instance ID
     * 
     * @param instanceId The instance ID
     * @return List of exports
     */
    List<ExporterEntityDocument> findByInstanceId(String instanceId);

    /**
     * Find active exports (PENDING or IN_PROGRESS) for an instance
     * 
     * @param instanceId The instance ID
     * @return List of active exports
     */
    List<ExporterEntityDocument> findActiveExports(String instanceId);

    /**
     * Find exports by status
     * 
     * @param status Export status
     * @return List of exports with the specified status
     */
    List<ExporterEntityDocument> findByStatus(ExporterEntityDocument.Status status);
}
