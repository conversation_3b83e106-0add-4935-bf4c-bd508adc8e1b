package it.circle.plugin.consumer;

import it.circle.crm.data.exporter.ExporterEntityDocument;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import it.circle.plugin.model.ExportMessage;
import it.circle.plugin.model.Target;
import it.circle.plugin.service.AccountExporterService;
import it.circle.plugin.service.ContactExporterService;
import it.circle.plugin.service.ExporterEntityService;
import it.circle.plugin.service.ManufacturerExporterService;
import it.circle.plugin.service.OrderExporterService;
import it.circle.plugin.service.ProductExporterService;
import it.circle.plugin.service.VenueExporterService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues="${queue.name.exporter}")
public class ConsumerExportEntity {

    private final ObjectMapper objectMapper;

    @Autowired
    private ContactExporterService contactExporterService;

    @Autowired
    private OrderExporterService orderExporterService;

    @Autowired
    private ProductExporterService productExporterService;

    @Autowired
    private AccountExporterService accountExporterService;

    @Autowired
    private VenueExporterService venueExporterService;

    @Autowired
    private ManufacturerExporterService manufacturerExporterService;

    @Autowired
    private ExporterEntityService exporterEntityService;

    @Autowired
    private FileCompletionEvent fileCompletionEvent;

    @RabbitHandler
    @SneakyThrows
    public void readMessage(byte[] message) {
        String messageReceived = new String(message);
        log.info("Received export message from queue: {}", messageReceived);

        ExporterEntityDocument exportDocument = null;

        try {
            // Parse the JSON message into our model
            ExportMessage exportMessage = objectMapper.readValue(messageReceived, ExportMessage.class);

            log.info("Parsed export message - Instance ID: {}, Owner: {}, Company: {}, Export ID: {}, Targets count: {}",
                    exportMessage.getInstanceId(),
                    exportMessage.getOwner(),
                    exportMessage.getCompanyName(),
                    exportMessage.getExportId(),
                    exportMessage.getTargets().size());

            // Create export tracking document
            exportDocument = exporterEntityService.createExport(
                exportMessage.getInstanceId(),
                exportMessage.getCompanyName(),
                exportMessage.getTargets()
            );

            log.info("Created export tracking document with ID: {}", exportDocument.getId());

            // Update status to IN_PROGRESS
            exporterEntityService.updateStatus(String.valueOf(exportDocument.getId()), ExporterEntityDocument.Status.IN_PROGRESS);

            // Process each target
            int totalTargets = exportMessage.getTargets().size();
            for (int i = 0; i < totalTargets; i++) {
                Target target = exportMessage.getTargets().get(i);

                // Update progress
                int progressPercentage = (i * 100) / totalTargets;
                exporterEntityService.updateProgress(String.valueOf(exportDocument.getId()), progressPercentage);

                // Process the target with export tracking
                processTarget(exportMessage.getInstanceId(), exportMessage.getOwner(), target, String.valueOf(exportDocument.getId()));
            }

            // Mark as completed (individual services will set the download link)
            log.info("All targets processed successfully for export: {}", exportDocument.getId());

        } catch (Exception e) {
            log.error("Error processing export message: {}", e.getMessage(), e);

            // Mark export as failed if we have the document
            if (exportDocument != null) {
                exporterEntityService.failExport(String.valueOf(exportDocument.getId()), e.getMessage());
            }

            throw e;
        }
    }

    private void processTarget(String instanceId, String owner, Target target, String exportId) {
        // Convert entity name to proper case for SettingsEntity lookup
        String entityName = normalizeEntityName(target.getEntity());

        // Create a new target with normalized entity name
        Target normalizedTarget = new Target();
        normalizedTarget.setEntity(entityName);
        normalizedTarget.setFields(target.getFields());
        normalizedTarget.setFilters(target.getFilters());

        try {
            switch (target.getEntity().toLowerCase()) {
                case "contact":
                    contactExporterService.processContactExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                case "order":
                    orderExporterService.processOrderExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                case "account":
                    accountExporterService.processAccountExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                case "product":
                    productExporterService.processProductExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                case "venue":
                    venueExporterService.processVenueExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                case "manufacturer":
                    manufacturerExporterService.processManufacturerExport(instanceId, owner, normalizedTarget, exportId);
                    break;
                default:
                    log.warn("Unknown entity type: {}", target.getEntity());
                    throw new IllegalArgumentException("Unknown entity type: " + target.getEntity());
            }
        } catch (Exception e) {
            log.error("Error processing target {} for export {}: {}", target.getEntity(), exportId, e.getMessage(), e);
            throw e; // Re-throw to be handled by the main export process
        }
    }

    /**
     * Normalize entity name to proper case for SettingsEntity lookup
     */
    private String normalizeEntityName(String entityName) {
        if (entityName == null || entityName.isEmpty()) {
            return entityName;
        }

        // Convert to proper case (first letter uppercase, rest as-is)
        return entityName.substring(0, 1).toUpperCase() + entityName.substring(1).toLowerCase();
    }


}