package it.circle.plugin.utility;

import it.circle.crm.api.commons.MimeType;
import it.circle.crm.api.file.CrmFile;
import it.circle.crm.api.file.CrmFileService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

@Slf4j
@Component
@RequiredArgsConstructor
public class MinioFileUploadUtility {

    private final CrmFileService crmFileService;

    /**
     * Upload Excel file to MinIO and return the download URI
     * 
     * @param excelFile ByteArrayOutputStream containing the Excel file
     * @param fileName File name without extension
     * @param companyId Company ID for the upload
     * @param instanceId Instance ID for the upload
     * @return Download URI from MinIO
     */
    public String uploadExcelFile(ByteArrayOutputStream excelFile, String fileName, String companyId, String instanceId) {
        log.info("Uploading Excel file to MinIO: {} for company: {}, instance: {}", fileName, companyId, instanceId);
        
        try {
            // Convert ByteArrayOutputStream to MultipartFile
            String fullFileName = fileName + ".xlsx";
            MultipartFile multipartFile = new ByteArrayMultipartFile(
                excelFile.toByteArray(), 
                fullFileName, 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            );
            
            // Upload to MinIO using CrmFileService
            CrmFile uploadedFile = crmFileService.uploadFile(companyId, instanceId, multipartFile);
            
            log.info("Successfully uploaded file to MinIO. URI: {}", uploadedFile.getUri());
            return uploadedFile.getUri();
            
        } catch (Exception e) {
            log.error("Failed to upload Excel file to MinIO: {}", fileName, e);
            throw new RuntimeException("Failed to upload file to MinIO: " + e.getMessage(), e);
        }
    }

    /**
     * Get file metadata from MinIO upload result
     * 
     * @param excelFile ByteArrayOutputStream containing the Excel file
     * @param fileName File name without extension
     * @param companyId Company ID for the upload
     * @param instanceId Instance ID for the upload
     * @return CrmFile with complete metadata
     */
    public CrmFile uploadAndGetMetadata(ByteArrayOutputStream excelFile, String fileName, String companyId, String instanceId) {
        log.info("Uploading Excel file to MinIO with metadata: {} for company: {}, instance: {}", fileName, companyId, instanceId);
        
        try {
            // Convert ByteArrayOutputStream to MultipartFile
            String fullFileName = fileName + ".xlsx";
            MultipartFile multipartFile = new ByteArrayMultipartFile(
                excelFile.toByteArray(), 
                fullFileName, 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            );
            
            // Upload to MinIO using CrmFileService
            CrmFile uploadedFile = crmFileService.uploadFile(companyId, instanceId, multipartFile);
            
            log.info("Successfully uploaded file to MinIO. File: {}, Size: {}, URI: {}", 
                    uploadedFile.getFilename(), uploadedFile.getSize(), uploadedFile.getUri());
            
            return uploadedFile;
            
        } catch (Exception e) {
            log.error("Failed to upload Excel file to MinIO: {}", fileName, e);
            throw new RuntimeException("Failed to upload file to MinIO: " + e.getMessage(), e);
        }
    }

    /**
     * Check if MinIO service is available
     * 
     * @return true if MinIO service is configured and available
     */
    public boolean isMinioAvailable() {
        return crmFileService != null;
    }

    /**
     * Custom MultipartFile implementation for ByteArray data
     */
    private static class ByteArrayMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String contentType;

        public ByteArrayMultipartFile(byte[] content, String name, String contentType) {
            this.content = content;
            this.name = name;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            throw new UnsupportedOperationException("transferTo not supported for ByteArrayMultipartFile");
        }
    }
}
