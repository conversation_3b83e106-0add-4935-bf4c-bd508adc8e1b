package it.circle.plugin.utility;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class FieldMappingUtility {
    
    /**
     * Map of common field name variations to standardized field names
     * This helps handle different naming conventions that might be used in requests
     */
    private static final Map<String, Set<String>> FIELD_ALIASES = new HashMap<>();
    
    static {
        // Order field aliases
        addFieldAliases("order_id", "orderid", "_id", "id");
        addFieldAliases("reference", "order_reference");
        addFieldAliases("total_paid", "totalpaid");
        addFieldAliases("order_date", "orderdate");
        addFieldAliases("customer", "customer_id", "customerid");
        addFieldAliases("contact_id", "contactid");
        addFieldAliases("venue", "venue_id", "venueid");
        addFieldAliases("payment_method", "paymentmethod");
        
        // Contact field aliases
        addFieldAliases("contact_id", "contactid", "_id", "id");
        addFieldAliases("first_name", "firstName", "firstname");
        addFieldAliases("last_name", "lastName", "lastname");
        addFieldAliases("full_name", "fullName", "fullname");
        addFieldAliases("email", "email_address", "primary_email");
        addFieldAliases("phone", "phone_number", "phonenumber", "primary_phone");
        addFieldAliases("company", "company_name", "organization");
        addFieldAliases("job_title", "jobtitle", "title");
        addFieldAliases("department", "dept");
        addFieldAliases("created", "created_date", "createddate");
        addFieldAliases("updated", "updated_date", "updateddate", "modified");
        
        // Account field aliases
        addFieldAliases("account_id", "accountid", "_id", "id");
        addFieldAliases("name", "account_name", "company_name");
        addFieldAliases("domain", "website", "website_url");
        addFieldAliases("email", "email_address", "primary_email");
        addFieldAliases("phone", "phone_number", "phonenumber");
        addFieldAliases("industry", "industry_type");
        addFieldAliases("activity", "activity_type");
        addFieldAliases("employees", "employee_count");
        addFieldAliases("revenue", "annual_revenue");
        addFieldAliases("postal_code", "postalcode", "zip", "zipcode");
        
        // Venue field aliases
        addFieldAliases("venue_id", "venueid", "_id", "id");
        addFieldAliases("name", "venue_name", "venuename");
        addFieldAliases("type", "venue_type", "venuetype");
        addFieldAliases("phone", "phone_number", "phonenumber");
        addFieldAliases("postal_code", "postalcode", "postcode", "zip");
        
        // Common aliases for all entities
        addFieldAliases("created", "created_date", "createddate", "created_at");
        addFieldAliases("updated", "updated_date", "updateddate", "modified", "modified_date", "updated_at");
        addFieldAliases("owner", "owner_id", "ownerid");
    }
    
    /**
     * Helper method to add field aliases
     */
    private static void addFieldAliases(String primaryField, String... aliases) {
        Set<String> aliasSet = FIELD_ALIASES.computeIfAbsent(primaryField, k -> new HashSet<>());
        aliasSet.add(primaryField.toLowerCase());
        for (String alias : aliases) {
            aliasSet.add(alias.toLowerCase());
        }
    }
    
    /**
     * Normalize field name to handle common variations
     * This method tries to map field name variations to a standardized form
     */
    public static String normalizeFieldName(String fieldName) {
        if (fieldName == null) {
            return null;
        }
        
        String lowerFieldName = fieldName.toLowerCase().trim();
        
        // Check if this field name matches any known aliases
        for (Map.Entry<String, Set<String>> entry : FIELD_ALIASES.entrySet()) {
            if (entry.getValue().contains(lowerFieldName)) {
                return entry.getKey();
            }
        }
        
        // Return original field name if no mapping found
        return lowerFieldName;
    }
    

    
    /**
     * Get all possible aliases for a field name
     */
    public static Set<String> getFieldAliases(String fieldName) {
        if (fieldName == null) {
            return Collections.emptySet();
        }
        
        String normalizedField = normalizeFieldName(fieldName);
        return FIELD_ALIASES.getOrDefault(normalizedField, Collections.singleton(fieldName.toLowerCase()));
    }
    
    /**
     * Find the best matching field name from a list of available fields
     * This is useful when the requested field name doesn't exactly match the available field names
     */
    public static Optional<String> findBestMatchingField(String requestedField, List<String> availableFields) {
        if (requestedField == null || availableFields == null || availableFields.isEmpty()) {
            return Optional.empty();
        }
        
        String normalizedRequested = normalizeFieldName(requestedField);
        
        // First, try exact match
        for (String availableField : availableFields) {
            if (normalizedRequested.equals(normalizeFieldName(availableField))) {
                return Optional.of(availableField);
            }
        }
        
        // Then try alias matching
        Set<String> requestedAliases = getFieldAliases(requestedField);
        for (String availableField : availableFields) {
            Set<String> availableAliases = getFieldAliases(availableField);
            for (String requestedAlias : requestedAliases) {
                if (availableAliases.contains(requestedAlias)) {
                    return Optional.of(availableField);
                }
            }
        }
        
        return Optional.empty();
    }

}
