package it.circle.plugin.service.impl;

import it.circle.crm.data.exporter.ExporterEntityDocument;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.ExporterEntityRepository;
import it.circle.plugin.service.ExporterEntityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExporterEntityServiceImpl implements ExporterEntityService {

    private final ExporterEntityRepository exporterEntityRepository;

    @Override
    public ExporterEntityDocument createExport(String instanceId, String companyId, List<Target> targets) {
        log.info("Creating new export for instance: {}, company: {}, targets: {}", 
                instanceId, companyId, targets.size());

        ExporterEntityDocument exportDocument = new ExporterEntityDocument();
        exportDocument.setInstanceId(instanceId);
        exportDocument.setCompanyId(companyId);
        exportDocument.setStatus(ExporterEntityDocument.Status.PENDING);
        exportDocument.setStartDate(Instant.now());
        exportDocument.setPercentage(0);
        
        // Convert Target objects to ExporterEntityTarget
        List<ExporterEntityDocument.ExporterEntityTarget> exportTargets = targets.stream()
                .map(this::convertToExporterTarget)
                .collect(Collectors.toList());
        
        exportDocument.setTargets(exportTargets);

        ExporterEntityDocument saved = exporterEntityRepository.save(exportDocument);
        log.info("Created export with ID: {}", saved.getId());
        
        return saved;
    }

    @Override
    public ExporterEntityDocument updateStatus(String exportId, ExporterEntityDocument.Status status) {
        log.info("Updating export {} status to: {}", exportId, status);
        
        Optional<ExporterEntityDocument> exportOpt = exporterEntityRepository.findById(exportId);
        if (exportOpt.isEmpty()) {
            log.error("Export not found with ID: {}", exportId);
            throw new RuntimeException("Export not found: " + exportId);
        }

        ExporterEntityDocument exportDocument = exportOpt.get();
        exportDocument.setStatus(status);
        
        if (status == ExporterEntityDocument.Status.IN_PROGRESS && exportDocument.getStartDate() == null) {
            exportDocument.setStartDate(Instant.now());
        }
        
        if (status == ExporterEntityDocument.Status.COMPLETED || status == ExporterEntityDocument.Status.FAILED) {
            exportDocument.setEndDate(Instant.now());
            if (status == ExporterEntityDocument.Status.COMPLETED && exportDocument.getPercentage() != 100) {
                exportDocument.setPercentage(100);
            }
        }

        return exporterEntityRepository.save(exportDocument);
    }

    @Override
    public ExporterEntityDocument updateProgress(String exportId, Integer percentage) {
        log.info("Updating export {} progress to: {}%", exportId, percentage);
        
        Optional<ExporterEntityDocument> exportOpt = exporterEntityRepository.findById(exportId);
        if (exportOpt.isEmpty()) {
            log.error("Export not found with ID: {}", exportId);
            throw new RuntimeException("Export not found: " + exportId);
        }

        ExporterEntityDocument exportDocument = exportOpt.get();
        exportDocument.setPercentage(Math.max(0, Math.min(100, percentage))); // Ensure 0-100 range
        
        // Auto-update status if needed
        if (exportDocument.getStatus() == ExporterEntityDocument.Status.PENDING) {
            exportDocument.setStatus(ExporterEntityDocument.Status.IN_PROGRESS);
            exportDocument.setStartDate(Instant.now());
        }

        return exporterEntityRepository.save(exportDocument);
    }

    @Override
    public ExporterEntityDocument completeExport(String exportId, String downloadLink) {
        log.info("Completing export {} with download link: {}", exportId, downloadLink);
        
        Optional<ExporterEntityDocument> exportOpt = exporterEntityRepository.findById(exportId);
        if (exportOpt.isEmpty()) {
            log.error("Export not found with ID: {}", exportId);
            throw new RuntimeException("Export not found: " + exportId);
        }

        ExporterEntityDocument exportDocument = exportOpt.get();
        exportDocument.setStatus(ExporterEntityDocument.Status.COMPLETED);
        exportDocument.setEndDate(Instant.now());
        exportDocument.setPercentage(100);
        exportDocument.setLink(downloadLink);
        exportDocument.setErrorMessage(null); // Clear any previous error

        ExporterEntityDocument saved = exporterEntityRepository.save(exportDocument);
        log.info("Export {} completed successfully", exportId);
        
        return saved;
    }

    @Override
    public ExporterEntityDocument failExport(String exportId, String errorMessage) {
        log.error("Failing export {} with error: {}", exportId, errorMessage);
        
        Optional<ExporterEntityDocument> exportOpt = exporterEntityRepository.findById(exportId);
        if (exportOpt.isEmpty()) {
            log.error("Export not found with ID: {}", exportId);
            throw new RuntimeException("Export not found: " + exportId);
        }

        ExporterEntityDocument exportDocument = exportOpt.get();
        exportDocument.setStatus(ExporterEntityDocument.Status.FAILED);
        exportDocument.setEndDate(Instant.now());
        exportDocument.setErrorMessage(errorMessage);

        return exporterEntityRepository.save(exportDocument);
    }

    @Override
    public Optional<ExporterEntityDocument> findById(String exportId) {
        return exporterEntityRepository.findById(exportId);
    }

    @Override
    public List<ExporterEntityDocument> findByInstanceId(String instanceId) {
        return exporterEntityRepository.findByInstanceId(instanceId);
    }

    @Override
    public List<ExporterEntityDocument> findActiveExports(String instanceId) {
        return exporterEntityRepository.findActiveExportsByInstanceId(instanceId);
    }

    @Override
    public List<ExporterEntityDocument> findByStatus(ExporterEntityDocument.Status status) {
        return exporterEntityRepository.findByStatus(status);
    }

    /**
     * Convert Target to ExporterEntityTarget
     */
    private ExporterEntityDocument.ExporterEntityTarget convertToExporterTarget(Target target) {
        ExporterEntityDocument.ExporterEntityTarget exportTarget = new ExporterEntityDocument.ExporterEntityTarget();
        exportTarget.setEntity(target.getEntity());
        
        // Extract field names from Fields object
        if (target.getFields() != null && target.getFields().getFieldsLinee() != null) {
            List<String> fieldNames = target.getFields().getFieldsHeader();
            exportTarget.setFields(fieldNames);
        }
        
        // Convert filters
        if (target.getFilters() != null) {
            List<ExporterEntityDocument.Filter> exportFilters = target.getFilters().stream()
                    .map(this::convertToExporterFilter)
                    .collect(Collectors.toList());
            exportTarget.setFilters(exportFilters);
        }
        
        return exportTarget;
    }

    /**
     * Convert Filter to ExporterEntityDocument.Filter
     */
    private ExporterEntityDocument.Filter convertToExporterFilter(it.circle.plugin.model.Filter filter) {
        ExporterEntityDocument.Filter exportFilter = new ExporterEntityDocument.Filter();
        exportFilter.setField(filter.getField());
        exportFilter.setOperator(filter.getOperator());
        exportFilter.setValues(filter.getValues());
        return exportFilter;
    }
}
