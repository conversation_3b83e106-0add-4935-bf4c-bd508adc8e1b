package it.circle.plugin.utility;

import it.circle.plugin.model.Filter;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class QueryBuilderUtility {

    private QueryBuilderUtility() {
        // Utility class - prevent instantiation
    }

    /**
     * Build MongoDB query from filters with optional instance filtering
     */
    public static Query buildQuery(List<Filter> filters, String instanceId) {
        Query query = new Query();
        
        // Add instance filter if needed
        if (instanceId != null && !instanceId.isEmpty()) {
            query.addCriteria(Criteria.where("instanceId").is(instanceId));
        }
        
        // Process each filter
        if (filters != null){
            for (Filter filter : filters) {
                Criteria criteria = buildCriteriaFromFilter(filter);
                if (criteria != null) {
                    query.addCriteria(criteria);
                }
            }
        }
        return query;
    }

    /**
     * Build MongoDB query from filters without instance filtering
     */
    public static Query buildQuery(List<Filter> filters) {
        return buildQuery(filters, null);
    }

    /**
     * Build MongoDB criteria from a single filter
     */
    public static Criteria buildCriteriaFromFilter(Filter filter) {
        String field = filter.getField();
        String operator = filter.getOperator().toLowerCase();
        String value = filter.getValues();

        if (value == null || value.trim().isEmpty()) {
            log.warn("No value provided for filter field: {}", field);
            return null;
        }

        Criteria criteria = Criteria.where(field);

        switch (operator) {
            case "eq", "equals" -> {
                if (isObjectId(value)) {
                    return criteria.is(new ObjectId(value));
                }
                return criteria.is(value);
            }
            case "ne", "not_equals" -> {
                if (isObjectId(value)) {
                    return criteria.ne(new ObjectId(value));
                }
                return criteria.ne(value);
            }
            case "in" -> {
                String[] valueArray = value.split(",");
                Object[] parsedValues = Arrays.stream(valueArray)
                        .map(String::trim)
                        .map(v -> isObjectId(v) ? new ObjectId(v) : v)
                        .toArray();
                return criteria.in(parsedValues);
            }
            case "nin", "not_in" -> {
                String[] valueArray = value.split(",");
                Object[] parsedValues = Arrays.stream(valueArray)
                        .map(String::trim)
                        .map(v -> isObjectId(v) ? new ObjectId(v) : v)
                        .toArray();
                return criteria.nin(parsedValues);
            }
            case "gt", "greater_than" -> {
                return criteria.gt(parseValue(value));
            }
            case "gte", "greater_than_equals" -> {
                return criteria.gte(parseValue(value));
            }
            case "lt", "less_than" -> {
                return criteria.lt(parseValue(value));
            }
            case "lte", "less_than_equals" -> {
                return criteria.lte(parseValue(value));
            }
            case "between" -> {
                String[] range = value.split(",");
                if (range.length == 2) {
                    Object startValue = parseValue(range[0].trim());
                    Object endValue = parseValue(range[1].trim());
                    log.info("Building 'between' criteria for field '{}' with range: {} to {}", field, startValue, endValue);
                    return criteria.gte(startValue).lte(endValue);
                } else {
                    log.warn("Invalid range format for 'between' operator. Expected: 'startValue,endValue', got: {}", value);
                    return null;
                }
            }
            case "like", "contains" -> {
                return criteria.regex(".*" + value + ".*", "i");
            }
            case "starts_with" -> {
                return criteria.regex("^" + value, "i");
            }
            case "ends_with" -> {
                return criteria.regex(value + "$", "i");
            }
            case "is_null" -> {
                return criteria.is(null);
            }
            case "is_not_null" -> {
                return criteria.ne(null);
            }
            default -> {
                log.warn("Unknown operator: {}, defaulting to equals", operator);
                if (isObjectId(value)) {
                    return criteria.is(new ObjectId(value));
                }
                return criteria.is(value);
            }
        }
    }

    private static boolean isObjectId(String value) {
        return value != null && value.matches("^[a-fA-F0-9]{24}$");
    }

    /**
     * Parse string value to appropriate type (handles dates, numbers, etc.)
     */
    public static Object parseValue(String value) {
        if (value == null) return null;
        
        // Try to parse as ISO date
        if (value.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.*")) {
            try {
                return LocalDateTime.parse(value.substring(0, 19), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            } catch (Exception e) {
                log.debug("Failed to parse as date: {}", value);
            }
        }
        
        // Try to parse as number
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            log.debug("Failed to parse as number: {}", value);
        }
        
        // Return as string
        return value;
    }

    /**
     * Build criteria for date range filtering
     */
    public static Criteria buildDateRangeCriteria(String field, LocalDateTime startDate, LocalDateTime endDate) {
        Criteria criteria = Criteria.where(field);
        
        if (startDate != null && endDate != null) {
            return criteria.gte(startDate).lte(endDate);
        } else if (startDate != null) {
            return criteria.gte(startDate);
        } else if (endDate != null) {
            return criteria.lte(endDate);
        }
        
        return null;
    }

    /**
     * Build criteria for numeric range filtering
     */
    public static Criteria buildNumericRangeCriteria(String field, Number minValue, Number maxValue) {
        Criteria criteria = Criteria.where(field);
        
        if (minValue != null && maxValue != null) {
            return criteria.gte(minValue).lte(maxValue);
        } else if (minValue != null) {
            return criteria.gte(minValue);
        } else if (maxValue != null) {
            return criteria.lte(maxValue);
        }
        
        return null;
    }

    /**
     * Build criteria for text search (case-insensitive)
     */
    public static Criteria buildTextSearchCriteria(String field, String searchText) {
        if (searchText == null || searchText.trim().isEmpty()) {
            return null;
        }
        
        return Criteria.where(field).regex(".*" + searchText.trim() + ".*", "i");
    }

    /**
     * Build criteria for multiple field OR search
     */
    public static Criteria buildMultiFieldSearchCriteria(List<String> fields, String searchText) {
        if (fields == null || fields.isEmpty() || searchText == null || searchText.trim().isEmpty()) {
            return null;
        }
        
        Criteria[] criteriaArray = fields.stream()
                .map(field -> Criteria.where(field).regex(".*" + searchText.trim() + ".*", "i"))
                .toArray(Criteria[]::new);
        
        return new Criteria().orOperator(criteriaArray);
    }
}
